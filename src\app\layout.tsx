import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { Toaster } from "@/components/ui/toaster"
import './globals.css';

const inter = Inter({ 
  subsets: ['latin'],
  variable: '--font-inter',
});

// Using a data URI for the SVG icon to avoid a missing file 404 error.
const modularIconDataUri = "data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M5.22877 9.1722L12.0001 5.25L18.7713 9.1722V15.0173L12.0001 18.9395L5.22877 15.0173V9.1722Z' stroke='white' strokeWidth='1.5' strokeLinecap='round' strokeLinejoin='round'/%3E%3Cpath d='M5.22877 9.1722L12.0001 13.0944L18.7713 9.1722' stroke='white' strokeWidth='1.5' strokeLinecap='round' strokeLinejoin='round'/%3E%3Cpath d='M12 13.0944V18.9395' stroke='white' strokeWidth='1.5' strokeLinecap='round' strokeLinejoin='round'/%3E%3C/svg%3E";

export const metadata: Metadata = {
  title: 'MODULAR - AI 3D Product Generator',
  description: 'Generate production-ready 3D models from text or images using AI.',
  icons: {
    icon: modularIconDataUri,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <body className={`${inter.variable} font-body antialiased`} suppressHydrationWarning>
        {children}
        <Toaster />
      </body>
    </html>
  );
}
