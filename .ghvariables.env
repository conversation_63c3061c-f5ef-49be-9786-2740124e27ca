################ UNIVERSAL VALUES HERE ################
#######################################################

############## NODE CREDENTIALS #######################
NODE_VERSION=22.15.0
#######################################################

############## FRONTEND SECRETS #######################
AWS_FRONTEND_SSM_ROLE_PATH=/slaves/frontend/role-arn
AWS_S3_BUILD_BUCKET=frontend-built-websites
AWS_AMPLIFY_STAGING_APP_NAME=Staging-Web-Apps
AWS_AMPLIFY_PRODUCTION_APP_NAME=Frontend-Web-Apps
AWS_AMPLIFY_STAGING_RESOURCE_NAME=ScottyDemo
AWS_AMPLIFY_PRODUCTION_RESOURCE_NAME=Scotty
AWS_AMPLIFY_PRODUCTION_DOMAIN=modularcx.io
AWS_AMPLIFY_PRODUCTION_SUBDOMAIN=scotty
NEXTJS_ENV_VARS={"NEXT_PUBLIC_API_URL":"https://api.modularcx.io","NEXT_PUBLIC_APP_ENV":"production"}
#######################################################

