"use server";

import { Client, type FileData } from "@gradio/client";

const GRADIO_HOST = "https://ai.modularcx.io//";

export type GradioGenerationOutput = {
  modelUrl: string;
  modelBase64: string;
  modelInfo: {
    vertices: number;
    faces: number;
    size: string;
  };
  secondaryFile?: string;
  generationInfo?: string;
  actualSeed?: number;
  finalProcessedFile?: string;
  postProcessingSettings?: {
    includeTexture: boolean;
    simplifyMesh: boolean;
  };
};

/**
 * Generates a 3D model by calling the Gradio API chain: /generation_all -> /lambda_3 -> /lambda_4
 * This function includes robust error handling for the known API instabilities.
 */
export type TransformOutput = {
  htmlOutput: string;
  downloadFile: FileData | null;
};

/**
 * Transforms a 3D model by calling the Gradio API chain: /lambda_5 -> /on_export_click
 * This function handles the transformation process for existing generated models.
 */
export async function transformModel(
  primaryFileUrl: string,
  secondaryFileUrl: string,
  options: {
    fileType?: string;
    reduceFace?: boolean;
    exportTexture?: boolean;
    targetFaceNum?: number;
  } = {}
): Promise<TransformOutput> {
  const {
    fileType = "glb",
    reduceFace = false,
    exportTexture = true,
    targetFaceNum = 10000,
  } = options;

  try {
    const client = await Client.connect(GRADIO_HOST);

    // Step 1: Call lambda_5 (initialization/setup)
    console.log("Step 1: Calling lambda_5 for initialization...");
    await client.predict("/lambda_5", {});

    // Step 2: Fetch the files and convert to blobs (required for Gradio client)
    console.log("Step 2: Fetching model files...");
    console.log("Primary file URL:", primaryFileUrl);
    console.log("Secondary file URL:", secondaryFileUrl);

    const [primaryResponse, secondaryResponse] = await Promise.all([
      fetch(primaryFileUrl),
      fetch(secondaryFileUrl),
    ]);

    if (!primaryResponse.ok || !secondaryResponse.ok) {
      throw new Error("Failed to fetch model files for transformation");
    }

    const primaryBlob = await primaryResponse.blob();
    const secondaryBlob = await secondaryResponse.blob();

    // Step 3: Call on_export_click with the files and options
    console.log("Step 3: Calling on_export_click for transformation...");
    console.log("Parameters being passed:", {
      file_type: fileType,
      file_type_type: typeof fileType,
      reduce_face: reduceFace,
      export_texture: exportTexture,
      target_face_num: targetFaceNum,
      primaryBlobType: typeof primaryBlob,
      secondaryBlobType: typeof secondaryBlob,
    });

    // Ensure fileType is a string
    const safeFileType = String(fileType);

    // Create proper FileData objects as shown in the example
    console.log("Creating proper FileData objects...");

    // Create FileData objects with the exact structure from the example
    const primaryFileData = {
      path: "", // Will be filled by Gradio
      url: primaryFileUrl,
      size: primaryBlob.size,
      orig_name: "white_mesh.obj",
      mime_type: null,
      is_stream: false,
      meta: { _type: "gradio.FileData" },
    };

    const secondaryFileData = {
      path: "", // Will be filled by Gradio
      url: secondaryFileUrl,
      size: secondaryBlob.size,
      orig_name: "textured_mesh.glb",
      mime_type: null,
      is_stream: false,
      meta: { _type: "gradio.FileData" },
    };

    console.log("FileData objects created:", {
      primaryFileData: { url: primaryFileData.url, size: primaryFileData.size },
      secondaryFileData: {
        url: secondaryFileData.url,
        size: secondaryFileData.size,
      },
    });

    const params = {
      file_out: primaryFileData,
      file_out2: secondaryFileData,
      file_type: "glb",
      reduce_face: false,
      export_texture: true,
      target_face_num: 10000,
    };

    console.log("Calling predict with FileData objects...");
    const transformResult = await client.predict("/on_export_click", params);

    const transformData = transformResult.data as any[];
    const htmlOutput = transformData[0] as string;
    const downloadFile = transformData[1] as FileData | null;

    return {
      htmlOutput,
      downloadFile,
    };
  } catch (error) {
    console.error("Transform API Error:", error);

    let errorMessage = "An unknown transformation error occurred.";

    if (typeof error === "object" && error !== null && "message" in error) {
      const gradioError = error as { message?: string };
      if (gradioError.message) {
        errorMessage = gradioError.message;
      }
    } else if (error instanceof Error) {
      errorMessage = error.message;
    }

    throw new Error(errorMessage);
  }
}

export async function generateModel(
  formData: FormData
): Promise<GradioGenerationOutput> {
  const imageFile = formData.get("image") as File | null;
  const inferenceSteps = Number(formData.get("inferenceSteps"));
  const octreeResolution = Number(formData.get("octreeResolution"));

  if (!imageFile) {
    throw new Error("Please upload an image to generate a model.");
  }

  try {
    const client = await Client.connect(GRADIO_HOST);
    const imageBlob = new Blob([imageFile], { type: imageFile.type });

    // Step 1: Generate the 3D model
    console.log("Step 1: Generating 3D model...");
    console.log("Using parameters:", {
      steps: Math.max(inferenceSteps, 30),
      octree_resolution: Math.max(octreeResolution, 256),
      guidance_scale: 5,
      seed: 1234,
      check_box_rembg: true,
      num_chunks: 8000,
      randomize_seed: true,
    });

    const generateResult = await client.predict("/generation_all", {
      image: imageBlob,
      mv_image_front: imageBlob,
      mv_image_back: imageBlob,
      mv_image_left: imageBlob,
      mv_image_right: imageBlob,
      steps: Math.max(inferenceSteps, 30), // Ensure minimum 30 steps
      guidance_scale: 5,
      seed: 1234,
      octree_resolution: Math.max(octreeResolution, 256), // Ensure minimum 256 resolution
      check_box_rembg: true,
      num_chunks: 8000,
      randomize_seed: true,
    });

    // Parse generation results - 5 items: [file1, file2, html_info, mesh_stats, actual_seed]
    const generateData = generateResult.data as any[];
    console.log("Generation API response:", {
      dataLength: generateData?.length,
      data0: generateData?.[0],
      data1: generateData?.[1],
      data2: generateData?.[2],
      data3: generateData?.[3],
      data4: generateData?.[4],
    });

    // Extract file data from the wrapped response structure
    const primaryModelFile = generateData[0]?.value || null;
    const secondaryModelFile = generateData[1]?.value || null;
    const generationInfo = generateData[2] as string | null;
    const meshStatsRaw = generateData[3] || null;
    const actualSeed = generateData[4] as number | null;

    // Parse mesh stats from the new structure
    const meshStats = meshStatsRaw
      ? {
          vertices: meshStatsRaw.number_of_vertices || 0,
          faces: meshStatsRaw.number_of_faces || 0,
        }
      : null;

    if (!primaryModelFile?.url) {
      console.error("Primary model file is invalid:", primaryModelFile);
      throw new Error("API did not return a valid model file.");
    }

    // Step 2: Get post-processing settings
    // console.log("Step 2: Getting post-processing settings...");
    const settingsResult = await client.predict("/lambda_3", {});
    const settingsData = settingsResult.data as any[];
    const includeTexture = settingsData[0] as boolean;
    const simplifyMesh = settingsData[1] as boolean;
    // settingsData[2] is the download button component

    // Step 3: Apply post-processing and get final file
    // console.log("Step 3: Applying post-processing...");
    const finalResult = await client.predict("/lambda_4", {});
    const finalData = finalResult.data as any[];
    const finalProcessedFile = finalData[0] as FileData | null;

    // Fetch the GLB model file and convert to base64 (prefer GLB over OBJ)
    const modelFileToFetch = secondaryModelFile || primaryModelFile;
    const modelResponse = await fetch(modelFileToFetch.url);
    if (!modelResponse.ok) {
      throw new Error(
        `Failed to fetch generated model: ${modelResponse.statusText}`
      );
    }
    const modelBuffer = await modelResponse.arrayBuffer();
    const modelBase64 = Buffer.from(modelBuffer).toString("base64");

    // Calculate file size.
    const fileSizeInBytes = modelBuffer.byteLength;
    const fileSizeInMB = (fileSizeInBytes / (1024 * 1024)).toFixed(2);

    return {
      modelUrl: secondaryModelFile?.url || primaryModelFile.url, // Use GLB file for viewer, fallback to OBJ
      modelBase64,
      modelInfo: {
        vertices: meshStats?.vertices || 0,
        faces: meshStats?.faces || 0,
        size: `${fileSizeInMB} MB`,
      },
      secondaryFile: primaryModelFile.url, // OBJ file as secondary
      generationInfo: generationInfo || undefined,
      actualSeed: actualSeed || undefined,
      finalProcessedFile: finalProcessedFile?.url,
      postProcessingSettings: {
        includeTexture,
        simplifyMesh,
      },
    };
  } catch (error) {
    console.error("Full Gradio API Error:", error);

    let errorMessage = "An unknown server error occurred.";

    if (typeof error === "object" && error !== null && "message" in error) {
      const gradioError = error as { message?: string };
      if (gradioError.message) {
        if (
          gradioError.message.includes(
            "'NoneType' object has no attribute 'faces'"
          )
        ) {
          errorMessage =
            "The API failed to generate mesh data for the model. This can happen with complex images. Please try a different image or adjust the settings.";
        } else {
          errorMessage = gradioError.message;
        }
      }
    } else if (error instanceof Error) {
      errorMessage = error.message;
    }

    throw new Error(errorMessage);
  }
}
