"use client";

import * as React from "react";
import type { ModelInfo } from "@/app/page";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { <PERSON>Viewer, type ThreeViewerHandle } from "./three-viewer";
import {
  View,
  RotateCw,
  Grid,
  Download,
  Package,
  Maximize,
  Minimize,
  RefreshCcw,
  Wand2,
} from "lucide-react";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { cn } from "@/lib/utils";

interface ViewerPanelProps {
  isLoading: boolean;
  generatedModelUrl: string | null;
  modelInfo: ModelInfo;
  isAutoRotate: boolean;
  setAutoRotate: (value: boolean) => void;
  isWireframe: boolean;
  setWireframe: (value: boolean) => void;
  selectedFormat: string;
  setSelectedFormat: (format: string) => void;
  exportTexture: boolean;
  setExportTexture: (value: boolean) => void;
  onExport: () => void;
  onTransform: () => void;
  isTransforming: boolean;
}

const InfoItem = ({
  label,
  value,
}: {
  label: string;
  value: string | number;
}) => (
  <div className="text-center">
    <p className="text-xs text-white/50">{label}</p>
    <p className="text-lg font-bold text-primary">{value}</p>
  </div>
);

export function ViewerPanel({
  isLoading,
  generatedModelUrl,
  modelInfo,
  isAutoRotate,
  setAutoRotate,
  isWireframe,
  setWireframe,
  selectedFormat,
  setSelectedFormat,
  exportTexture,
  setExportTexture,
  onExport,
  onTransform,
  isTransforming,
}: ViewerPanelProps) {
  const threeViewerRef = React.useRef<ThreeViewerHandle>(null);
  const viewerPanelRef = React.useRef<HTMLDivElement>(null);
  const [isFullscreen, setIsFullscreen] = React.useState(false);

  const toggleAutoRotate = () => setAutoRotate(!isAutoRotate);
  const toggleWireframe = () => setWireframe(!isWireframe);
  const resetCamera = () => threeViewerRef.current?.resetCamera();

  const handleFullscreenChange = React.useCallback(() => {
    setIsFullscreen(!!document.fullscreenElement);
  }, []);

  React.useEffect(() => {
    document.addEventListener("fullscreenchange", handleFullscreenChange);
    return () =>
      document.removeEventListener("fullscreenchange", handleFullscreenChange);
  }, [handleFullscreenChange]);

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      viewerPanelRef.current?.requestFullscreen().catch((err) => {
        console.error(
          `Error attempting to enable full-screen mode: ${err.message} (${err.name})`
        );
      });
    } else {
      document.exitFullscreen();
    }
  };

  return (
    <Card
      ref={viewerPanelRef}
      className={cn(
        "glass-panel p-6 flex flex-col gap-6 overflow-y-auto",
        isFullscreen && "bg-black/95 backdrop-blur-xl"
      )}
    >
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-3">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-br from-primary to-blue-700 text-white">
            <View className="h-5 w-5" />
          </div>
          <div>
            <h2 className="text-lg font-bold">3D Model Viewer</h2>
            <p className="text-sm text-white/50">
              Interactive preview of your model
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={resetCamera}
            className="hover:text-primary hover:bg-white/10"
          >
            <RefreshCcw className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleWireframe}
            className={cn("hover:bg-white/10", {
              "bg-primary/20 text-primary hover:bg-primary/30": isWireframe,
            })}
          >
            <Grid className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleAutoRotate}
            className={cn("hover:bg-white/10", {
              "bg-primary/20 text-primary hover:bg-primary/30": isAutoRotate,
            })}
          >
            <RotateCw className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleFullscreen}
            className="hover:text-primary hover:bg-white/10"
          >
            {isFullscreen ? (
              <Minimize className="h-4 w-4" />
            ) : (
              <Maximize className="h-4 w-4" />
            )}
          </Button>
        </div>
      </div>

      <div
        className={cn(
          "relative aspect-square w-full bg-black/50 rounded-lg overflow-hidden border border-white/10 shadow-inner",
          isFullscreen && "flex-1 aspect-auto"
        )}
      >
        <ThreeViewer
          ref={threeViewerRef}
          modelUrl={generatedModelUrl}
          autoRotate={isAutoRotate}
          wireframe={isWireframe}
        />

        {isLoading && !generatedModelUrl && (
          <div className="absolute inset-0 bg-black/80 backdrop-blur-sm flex flex-col items-center justify-center text-center p-4 z-10">
            <div className="relative w-24 h-24">
              <div className="absolute inset-0 border-4 border-primary/20 rounded-full"></div>
              <div className="absolute inset-0 border-4 border-primary rounded-full animate-spin-slow border-t-transparent"></div>
              <div className="absolute inset-0 flex items-center justify-center text-2xl font-bold text-primary">
                <View className="w-10 h-10 animate-pulse" />
              </div>
            </div>
            <p className="text-lg font-semibold mt-6">Generating 3D Model...</p>
            <p className="text-sm text-white/60">
              AI is creating the 3D structure, please wait.
            </p>
          </div>
        )}

        {!generatedModelUrl && !isLoading && (
          <div className="absolute inset-0 flex flex-col items-center justify-center text-center p-4">
            <Package className="w-16 h-16 text-white/10" />
            <p className="mt-4 text-lg font-semibold text-white/70">
              No Model Generated
            </p>
            <p className="text-sm text-white/50">Upload an image to start.</p>
          </div>
        )}
      </div>

      <div className={cn("flex flex-col gap-6", isFullscreen && "hidden")}>
        {modelInfo && (
          <div className="grid grid-cols-3 gap-4">
            <InfoItem
              label="Vertices"
              value={modelInfo.vertices.toLocaleString()}
            />
            <InfoItem label="Faces" value={modelInfo.faces.toLocaleString()} />
            <InfoItem label="File Size" value={modelInfo.size} />
          </div>
        )}

        <Card className="bg-white/5 border-white/10 p-4">
          <h3 className="text-sm font-semibold mb-3">Export Options</h3>
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 mb-4">
            {["glb", "obj", "ply", "stl"].map((format) => (
              <Button
                key={format}
                variant={selectedFormat === format ? "default" : "outline"}
                className="uppercase"
                onClick={() => setSelectedFormat(format)}
              >
                {format}
              </Button>
            ))}
          </div>

          <div className="flex items-center justify-between mt-4">
            <Label htmlFor="export-texture" className="cursor-pointer">
              Export with Texture
            </Label>
            <Switch
              id="export-texture"
              checked={exportTexture}
              onCheckedChange={setExportTexture}
            />
          </div>

          <Button
            size="lg"
            className="w-full mt-4 bg-gradient-to-br from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 hover:shadow-[0_0_15px_-5px_rgb(147,51,234)] transition-all"
            disabled={!generatedModelUrl || isLoading || isTransforming}
            onClick={onTransform}
          >
            <Wand2 className="mr-2 h-4 w-4" />
            {isTransforming ? "Transforming..." : "Transform"}
          </Button>

          <Button
            size="lg"
            className="w-full mt-2 hover:shadow-[0_0_15px_-5px_hsl(var(--primary))] transition-shadow"
            disabled={!generatedModelUrl || isLoading}
            onClick={onExport}
          >
            <Download className="mr-2 h-4 w-4" />
            Export Model
          </Button>
        </Card>
      </div>
    </Card>
  );
}
