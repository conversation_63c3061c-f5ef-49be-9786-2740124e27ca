version: 1
frontend:
  phases:
    preBuild:
      commands:
        - echo "Installing dependencies..."
        - npm ci --legacy-peer-deps
    build:
      commands:
        - echo "Building Next.js application with SSR support..."
        - npm run build
  artifacts:
    baseDirectory: .next/standalone # Changed for standalone build
    files:
      - "**/*"
  cache:
    paths:
      - node_modules/**/*
      - .next/cache/**/*
backend:
  phases:
    build:
      commands:
        - echo "No backend build steps"
