@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 224 71% 4%;
    --foreground: 210 20% 98%;
    --card: 224 71% 4%;
    --card-foreground: 210 20% 98%;
    --popover: 224 71% 4%;
    --popover-foreground: 210 20% 98%;
    --primary: 210 100% 50%;
    --primary-foreground: 210 20% 98%;
    --secondary: 210 20% 20%;
    --secondary-foreground: 210 20% 98%;
    --muted: 210 20% 15%;
    --muted-foreground: 210 20% 60%;
    --accent: 210 100% 45%;
    --accent-foreground: 210 20% 98%;
    --destructive: 0 72% 51%;
    --destructive-foreground: 210 20% 98%;
    --border: 210 20% 25%;
    --input: 210 20% 15%;
    --ring: 210 100% 50%;
    --radius: 1.5rem;
  }
 
  .dark {
    --background: 224 71% 4%;
    --foreground: 210 20% 98%;
    --card: 224 71% 4%;
    --card-foreground: 210 20% 98%;
    --popover: 224 71% 4%;
    --popover-foreground: 210 20% 98%;
    --primary: 210 100% 50%;
    --primary-foreground: 210 20% 98%;
    --secondary: 210 20% 20%;
    --secondary-foreground: 210 20% 98%;
    --muted: 210 20% 15%;
    --muted-foreground: 210 20% 60%;
    --accent: 210 100% 45%;
    --accent-foreground: 210 20% 98%;
    --destructive: 0 63% 31%;
    --destructive-foreground: 210 20% 98%;
    --border: 210 20% 25%;
    --input: 210 20% 15%;
    --ring: 210 100% 50%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  html {
    font-feature-settings: "cv11", "ss01";
  }
  body {
    @apply bg-background text-foreground;
    background: #000;
  }

  body::before {
      content: '';
      position: fixed;
      inset: 0;
      background-image: 
          url("data:image/svg+xml,%3Csvg width='100' height='100' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noise'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='4' /%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noise)' opacity='0.02'/%3E%3C/svg%3E");
      pointer-events: none;
      z-index: -1;
  }

  body::after {
      content: '';
      position: fixed;
      inset: 0;
      background: radial-gradient(
          circle at 20% 50%,
          hsl(var(--primary) / 0.15) 0%,
          transparent 50%
      ),
      radial-gradient(
          circle at 80% 80%,
          hsl(var(--primary) / 0.1) 0%,
          transparent 50%
      );
      pointer-events: none;
      z-index: -2;
  }
}

@layer utilities {
    .glass-panel {
        @apply relative overflow-hidden rounded-3xl border border-white/10 bg-white/5 backdrop-blur-2xl shadow-2xl shadow-black/20;
    }
    .glass-panel-hover {
        @apply transition-all duration-300 ease-in-out;
    }
    .glass-panel-hover:hover {
        @apply border-white/20 bg-white/[0.07] -translate-y-0.5 shadow-2xl shadow-black/40;
    }

    .glow-pulse {
      animation: glow-pulse 2s infinite;
    }
    @keyframes glow-pulse {
        0%, 100% { opacity: 0.5; transform: scale(1); }
        50% { opacity: 1; transform: scale(1.2); }
    }
    
    .thinking-indicator {
      animation: spin 1s linear infinite;
    }
    
    .thinking-indicator::before {
      content: '';
      position: absolute;
      inset: 10px;
      border: 2px solid transparent;
      border-top-color: hsl(var(--primary) / 0.6);
      border-radius: 50%;
      animation: spin 0.7s linear infinite reverse;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }
}
