'use client';

import * as React from 'react';
import type { ChangeEvent } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Camera, UploadCloud, Trash2, Settings } from 'lucide-react';
import Image from 'next/image';

interface ControlPanelProps {
  imagePreview: string | null;
  handleImageChange: (e: ChangeEvent<HTMLInputElement>) => void;
  removeImage: () => void;
  onGenerate: () => void;
  isLoading: boolean;
  inferenceSteps: number;
  setInferenceSteps: (value: number) => void;
  octreeResolution: number;
  setOctreeResolution: (value: number) => void;
}

export function ControlPanel({
  imagePreview,
  handleImageChange,
  removeImage,
  onGenerate,
  isLoading,
  inferenceSteps,
  setInferenceSteps,
  octreeResolution,
  setOctreeResolution,
}: ControlPanelProps) {
  return (
    <div className="flex flex-col gap-6">
      <Card className="glass-panel p-6">
        <div className="flex items-center gap-3 mb-6">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-br from-primary to-blue-700 text-white">
            <Camera className="h-5 w-5" />
          </div>
          <div>
            <h2 className="text-lg font-bold">Image to 3D</h2>
            <p className="text-sm text-white/50">
              Upload a PNG or JPG to generate a model.
            </p>
          </div>
        </div>

        <div>
          {!imagePreview ? (
            <label className="relative flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer bg-white/5 border-white/20 hover:border-primary hover:bg-primary/10 transition-colors">
              <div className="flex flex-col items-center justify-center pt-5 pb-6 text-center">
                <UploadCloud className="w-8 h-8 mb-2 text-white/50" />
                <p className="mb-1 text-sm font-semibold">
                  Click to upload or drag & drop
                </p>
                <p className="text-xs text-white/50">PNG, JPG (MAX. 10MB)</p>
              </div>
              <Input
                id="imageFile"
                type="file"
                className="hidden"
                accept="image/png, image/jpeg"
                onChange={handleImageChange}
                disabled={isLoading}
              />
            </label>
          ) : (
            <div className="relative group">
              <Image
                src={imagePreview}
                alt="Preview"
                width={420}
                height={128}
                className="w-full h-32 object-cover rounded-lg"
              />
              <div className="absolute inset-0 bg-black/50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                <Button variant="destructive" size="icon" onClick={removeImage} disabled={isLoading}>
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </div>
      </Card>
      <Card className="glass-panel p-6">
        <CardHeader className="p-0 mb-6">
          <div className="flex items-center gap-3">
            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-br from-primary to-blue-700 text-white">
              <Settings className="h-5 w-5" />
            </div>
            <div>
              <CardTitle className="text-lg font-bold">Advanced Settings</CardTitle>
              <CardDescription className="text-sm text-white/50">
                Adjust generation parameters.
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0 flex flex-col gap-6">
          <div className="grid gap-2">
            <div className="flex justify-between items-center">
              <Label htmlFor="inference-steps">Inference Steps</Label>
              <span className="text-sm font-medium text-primary">{inferenceSteps}</span>
            </div>
            <Slider
              id="inference-steps"
              min={1}
              max={30}
              step={1}
              value={[inferenceSteps]}
              onValueChange={(vals) => setInferenceSteps(vals[0])}
              disabled={isLoading}
            />
          </div>
          <div className="grid gap-2">
            <div className="flex justify-between items-center">
              <Label htmlFor="octree-resolution">Octree Resolution</Label>
              <span className="text-sm font-medium text-primary">{octreeResolution}</span>
            </div>
            <Slider
              id="octree-resolution"
              min={16}
              max={256}
              step={16}
              value={[octreeResolution]}
              onValueChange={(vals) => setOctreeResolution(vals[0])}
              disabled={isLoading}
            />
          </div>
        </CardContent>
      </Card>
      <Button
        size="lg"
        className="w-full text-base font-bold tracking-wider py-7 bg-gradient-to-br from-primary to-blue-700 hover:shadow-[0_0_20px_-5px_hsl(var(--primary))] transition-all duration-300 transform hover:-translate-y-1"
        onClick={onGenerate}
        disabled={isLoading || !imagePreview}
      >
        {isLoading ? 'Generating...' : 'Generate 3D Model'}
      </Button>
    </div>
  );
}
