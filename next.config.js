// next.config.js - Final config for static export deployment

/** @type {import('next').NextConfig} */
const nextConfig = {
  // Standalone build for SSR deployment (supports Server Actions)
  output: "standalone",

  // Trailing slash consistency
  trailingSlash: true,

  // TypeScript configuration
  typescript: {
    ignoreBuildErrors: true,
  },

  // ESLint configuration
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Image configuration for SSR deployment
  images: {
    // Keep optimized images for SSR (remove unoptimized)
    remotePatterns: [
      {
        protocol: "https",
        hostname: "placehold.co",
        port: "",
        pathname: "/**",
      },
    ],
  },

  // Environment variables (optional)
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },

  // Optional: Add base path if deploying to subdirectory
  // basePath: '/my-app',

  // Optional: Asset prefix for CDN
  // assetPrefix: 'https://cdn.example.com',
};

module.exports = nextConfig;
