"use client";

import * as React from "react";
import type { ChangeEvent } from "react";
import { Header } from "@/components/vismodel/header";
import { ControlPanel } from "@/components/vismodel/control-panel";
import { ViewerPanel } from "@/components/vismodel/viewer-panel";
import { useToast } from "@/hooks/use-toast";
import { generateModel, transformModel } from "@/lib/gradio";
import { exportModelClient } from "@/lib/export-client";

export type ModelInfo = {
  vertices: number;
  faces: number;
  size: string;
} | null;

export default function Home() {
  const { toast } = useToast();
  const [imageFile, setImageFile] = React.useState<File | null>(null);
  const [imagePreview, setImagePreview] = React.useState<string | null>(null);

  const [isLoading, setIsLoading] = React.useState(false);
  const [generatedModelUrl, setGeneratedModelUrl] = React.useState<
    string | null
  >(null);
  const [generatedModelBase64, setGeneratedModelBase64] = React.useState<
    string | null
  >(null);
  const [modelInfo, setModelInfo] = React.useState<ModelInfo>(null);
  const [finalProcessedFileUrl, setFinalProcessedFileUrl] = React.useState<
    string | null
  >(null);

  // Advanced settings state
  const [inferenceSteps, setInferenceSteps] = React.useState(1);
  const [octreeResolution, setOctreeResolution] = React.useState(16);

  const [isAutoRotate, setAutoRotate] = React.useState(true);
  const [isWireframe, setWireframe] = React.useState(false);
  const [selectedFormat, setSelectedFormat] = React.useState("glb");
  const [exportTexture, setExportTexture] = React.useState(true);

  // Transform state
  const [isTransforming, setIsTransforming] = React.useState(false);
  const [secondaryFileUrl, setSecondaryFileUrl] = React.useState<string | null>(
    null
  );

  // Clean up object URL to avoid memory leaks
  React.useEffect(() => {
    return () => {
      if (imagePreview) {
        URL.revokeObjectURL(imagePreview);
      }
    };
  }, [imagePreview]);

  const handleImageChange = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (file.size > 10 * 1024 * 1024) {
        // 10MB limit
        toast({
          variant: "destructive",
          title: "File too large",
          description: "Image size must be less than 10MB.",
        });
        return;
      }
      setImageFile(file);
      // Revoke previous object URL if it exists
      if (imagePreview) {
        URL.revokeObjectURL(imagePreview);
      }
      setImagePreview(URL.createObjectURL(file));
    }
  };

  const removeImage = () => {
    setImageFile(null);
    if (imagePreview) {
      URL.revokeObjectURL(imagePreview);
    }
    setImagePreview(null);
    const input = document.getElementById("imageFile") as HTMLInputElement;
    if (input) input.value = "";
  };

  const handleExport = async () => {
    if (!generatedModelBase64) {
      toast({
        variant: "destructive",
        title: "No model to export",
        description: "Please generate a model first.",
      });
      return;
    }

    const exportToast = toast({
      title: "Exporting Model...",
      description: `Preparing your .${selectedFormat.toUpperCase()} file.`,
    });

    try {
      const downloadUrl = await exportModelClient({
        modelBase64: generatedModelBase64,
        fileType: selectedFormat,
        export_texture: exportTexture,
        finalProcessedFileUrl: finalProcessedFileUrl || undefined,
      });

      const link = document.createElement("a");
      link.href = downloadUrl;
      link.setAttribute("download", `model_export.${selectedFormat}`);
      link.setAttribute("target", "_blank");
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      exportToast.update({
        id: exportToast.id,
        title: "Export Successful!",
        description: `Your model has been downloaded.`,
      });
    } catch (error) {
      console.error("Export failed:", error);
      exportToast.update({
        id: exportToast.id,
        variant: "destructive",
        title: "Export Failed",
        description: "Could not prepare the model for download.",
      });
    }
  };

  const handleGenerate = async () => {
    if (!imageFile) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Please upload a product image.",
      });
      return;
    }

    setIsLoading(true);
    setGeneratedModelUrl(null);
    setGeneratedModelBase64(null);
    setModelInfo(null);
    setFinalProcessedFileUrl(null);

    try {
      const formData = new FormData();
      formData.append("image", imageFile);
      formData.append("inferenceSteps", inferenceSteps.toString());
      formData.append("octreeResolution", octreeResolution.toString());

      const result = await generateModel(formData);

      setGeneratedModelUrl(result.modelUrl);
      setGeneratedModelBase64(result.modelBase64);
      setModelInfo(result.modelInfo);
      setFinalProcessedFileUrl(result.finalProcessedFile || null);
      setSecondaryFileUrl(result.secondaryFile || null);

      toast({
        title: "Success!",
        description: "3D model generated successfully.",
      });
    } catch (error) {
      console.error("Generation failed:", error);
      const errorMessage =
        error instanceof Error ? error.message : JSON.stringify(error);
      toast({
        variant: "destructive",
        title: "Generation Failed",
        description: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleTransform = async () => {
    if (!generatedModelUrl || !secondaryFileUrl) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Please generate a mesh first.",
      });
      return;
    }

    setIsTransforming(true);

    const transformToast = toast({
      title: "Transforming Model...",
      description: "Processing your 3D model transformation.",
    });

    try {
      console.log("Transform parameters:", {
        primaryFile: secondaryFileUrl,
        secondaryFile: generatedModelUrl,
        fileType: selectedFormat,
        exportTexture: exportTexture,
      });

      const result = await transformModel(secondaryFileUrl, generatedModelUrl, {
        fileType: selectedFormat,
        reduceFace: false, // You can make this configurable if needed
        exportTexture: exportTexture,
        targetFaceNum: 10000, // You can make this configurable if needed
      });

      if (result.downloadFile?.url) {
        // Automatically download the transformed file
        const link = document.createElement("a");
        link.href = result.downloadFile.url;
        link.setAttribute("download", `transformed_model.${selectedFormat}`);
        link.setAttribute("target", "_blank");
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        transformToast.update({
          id: transformToast.id,
          title: "Transform Successful!",
          description: "Your transformed model has been downloaded.",
        });
      } else {
        throw new Error("No download file received from transformation");
      }
    } catch (error) {
      console.error("Transform failed:", error);
      const errorMessage =
        error instanceof Error ? error.message : JSON.stringify(error);
      transformToast.update({
        id: transformToast.id,
        variant: "destructive",
        title: "Transform Failed",
        description: errorMessage,
      });
    } finally {
      setIsTransforming(false);
    }
  };

  return (
    <div className="relative z-10 h-screen overflow-hidden">
      <Header />
      <main className="mx-auto max-w-7xl h-full px-4 sm:px-6 lg:px-8 pt-28 pb-8">
        <div className="grid grid-cols-1 md:grid-cols-[350px_1fr] gap-8 h-full">
          <div className="overflow-y-auto">
            <ControlPanel
              imagePreview={imagePreview}
              handleImageChange={handleImageChange}
              removeImage={removeImage}
              onGenerate={handleGenerate}
              isLoading={isLoading}
              inferenceSteps={inferenceSteps}
              setInferenceSteps={setInferenceSteps}
              octreeResolution={octreeResolution}
              setOctreeResolution={setOctreeResolution}
            />
          </div>
          <ViewerPanel
            isLoading={isLoading}
            generatedModelUrl={generatedModelUrl}
            modelInfo={modelInfo}
            isAutoRotate={isAutoRotate}
            setAutoRotate={setAutoRotate}
            isWireframe={isWireframe}
            setWireframe={setWireframe}
            selectedFormat={selectedFormat}
            setSelectedFormat={setSelectedFormat}
            exportTexture={exportTexture}
            setExportTexture={setExportTexture}
            onExport={handleExport}
            onTransform={handleTransform}
            isTransforming={isTransforming}
          />
        </div>
      </main>
    </div>
  );
}
