{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Testing /generate endpoint...\n", "Status: 200\n", "Success! GLB file saved as 'test_output.glb'\n"]}], "source": ["import requests\n", "import base64\n", "from PIL import Image\n", "import io\n", "\n", "# Create a simple test image\n", "def create_test_image():\n", "    img = Image.new('RGB', (256, 256), color='red')\n", "    buffer = io.BytesIO()\n", "    img.save(buffer, format='PNG')\n", "    return base64.b64encode(buffer.getvalue()).decode()\n", "\n", "# Test the synchronous /generate endpoint\n", "API_URL = \"http://localhost:8081\"\n", "\n", "# Minimal request with only required parameter\n", "request_data = {\n", "    \"image\": create_test_image()\n", "}\n", "\n", "print(\"Testing /generate endpoint...\")\n", "try:\n", "    response = requests.post(f\"{API_URL}/generate\", json=request_data)\n", "    print(f\"Status: {response.status_code}\")\n", "    \n", "    if response.status_code == 200:\n", "        # Save the generated GLB file\n", "        with open(\"test_output.glb\", \"wb\") as f:\n", "            f.write(response.content)\n", "        print(\"Success! GLB file saved as 'test_output.glb'\")\n", "    else:\n", "        print(f\"Error: {response.text}\")\n", "        \n", "except requests.exceptions.ConnectionError:\n", "    print(\"Error: Could not connect to API server at localhost:8081\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 2}