"use client";

import * as React from "react";
import { Canvas } from "@react-three/fiber";
import {
  OrbitControls,
  Environment,
  Grid,
  ContactShadows,
  Bounds,
  useGLTF,
  Loader,
} from "@react-three/drei";
import { Suspense } from "react";
import { Mesh } from "three";

export interface ThreeViewerProps {
  modelUrl: string | null;
  autoRotate: boolean;
  wireframe: boolean;
}

export interface ThreeViewerHandle {
  resetCamera: () => void;
}

function Model({ url, wireframe }: { url: string; wireframe: boolean }) {
  const { scene } = useGLTF(url);
  const copiedScene = React.useMemo(() => scene.clone(), [scene]);

  React.useEffect(() => {
    copiedScene.traverse((child) => {
      if ((child as Mesh).isMesh) {
        const mesh = child as Mesh;

        // Ensure material is an array or single object
        const materials = Array.isArray(mesh.material)
          ? mesh.material
          : [mesh.material];

        materials.forEach((material) => {
          if ("wireframe" in material) {
            material.wireframe = wireframe;
          }
        });

        mesh.castShadow = true;
        mesh.receiveShadow = true;
      }
    });
  }, [wireframe, copiedScene]);

  return <primitive object={copiedScene} />;
}

export const ThreeViewer = React.forwardRef<
  ThreeViewerHandle,
  ThreeViewerProps
>(({ modelUrl, autoRotate, wireframe }, ref) => {
  const controlsRef = React.useRef<any>(null);

  React.useImperativeHandle(ref, () => ({
    resetCamera: () => {
      controlsRef.current?.reset();
    },
  }));

  // Temporary fallback to debug the Span error
  if (!modelUrl) {
    return (
      <div className="flex items-center justify-center h-full bg-gray-900 text-white">
        <p>No model loaded</p>
      </div>
    );
  }

  try {
    return (
      <Canvas shadows dpr={[1, 2]} camera={{ fov: 45 }}>
        <color attach="background" args={["#141418"]} />

        <ambientLight intensity={0.5} />
        <spotLight
          intensity={0.5}
          angle={0.1}
          penumbra={1}
          position={[10, 15, 10]}
          castShadow
        />

        <Environment preset="apartment" />

        <Grid
          position={[0, -1.01, 0]}
          args={[10, 10]}
          infiniteGrid
          fadeDistance={30}
          fadeStrength={1}
          cellColor={"hsl(210, 20%, 25%)"}
          sectionColor={"hsl(210, 100%, 45%)"}
        />

        <ContactShadows
          position={[0, -1, 0]}
          opacity={0.75}
          scale={10}
          blur={2}
          far={2}
        />

        <Suspense fallback={null}>
          <Bounds fit clip observe margin={1.2}>
            <Model url={modelUrl} wireframe={wireframe} />
          </Bounds>
        </Suspense>

        <OrbitControls
          ref={controlsRef}
          autoRotate={autoRotate}
          autoRotateSpeed={0.5}
          minDistance={2}
          maxDistance={50}
          minPolarAngle={Math.PI / 4}
          maxPolarAngle={Math.PI / 2}
        />
      </Canvas>
    );
  } catch (error) {
    console.error("ThreeViewer error:", error);
    return (
      <div className="flex items-center justify-center h-full bg-red-900 text-white">
        <div className="text-center">
          <p>3D Viewer Error</p>
          <p className="text-sm mt-2">Check console for details</p>
          <p className="text-xs mt-1">Model URL: {modelUrl}</p>
        </div>
      </div>
    );
  }
});

ThreeViewer.displayName = "ThreeViewer";
