'use client';
import { Flame } from 'lucide-react';
import { ModularIcon } from './icons';

export function Header() {
  return (
    <header className="fixed top-0 left-0 right-0 z-50 p-4">
      <div className="glass-panel-hover mx-auto max-w-7xl rounded-3xl border border-white/10 bg-black/30 p-4 backdrop-blur-2xl">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="relative flex h-10 w-10 items-center justify-center rounded-xl bg-primary shadow-lg shadow-primary/30">
              <ModularIcon className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-white tracking-widest">MODULAR</h1>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2 rounded-full border border-green-500/30 bg-green-500/10 px-3 py-1.5 text-xs text-green-300">
              <div className="relative flex h-2 w-2">
                <span className="absolute inline-flex h-full w-full animate-ping rounded-full bg-green-400 opacity-75"></span>
                <span className="relative inline-flex h-2 w-2 rounded-full bg-green-500"></span>
              </div>
              <span>Modular AI Connected</span>
            </div>
            <div className="hidden sm:flex items-center gap-2 rounded-full border border-border bg-white/5 px-3 py-1.5 text-xs text-white/80">
              <Flame className="h-3 w-3 text-orange-400" />
              <span>Credits: 250</span>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}
