"use client";

export type ExportModelParams = {
  modelBase64: string;
  fileType: string;
  export_texture: boolean;
  finalProcessedFileUrl?: string;
};

/** test
 * Client-side export function for 3D models
 * Uses the final processed file from the API chain if available
 */
export async function exportModelClient(
  params: ExportModelParams
): Promise<string> {
  // If we have the final processed file URL from lambda_4, use that
  if (params.finalProcessedFileUrl) {
    try {
      const response = await fetch(params.finalProcessedFileUrl);
      if (response.ok) {
        const blob = await response.blob();
        return URL.createObjectURL(blob);
      }
    } catch (error) {
      console.warn(
        "Failed to fetch final processed file, falling back to base64:",
        error
      );
    }
  }

  // Fallback: Convert base64 back to blob for download
  const binaryString = atob(params.modelBase64);
  const bytes = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  const blob = new Blob([bytes], { type: "application/octet-stream" });
  return URL.createObjectURL(blob);
}
